<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>eHroby - <PERSON>r<PERSON><PERSON> cenov<PERSON>ch pon<PERSON></title>
    <meta name="description" content="Profesionálny generátor PDF cenových ponúk pre služby starostlivosti o hrobové miesta">
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState } = React;

        // Cenové tabuľky podľa špecifikácie
        const PRICES = {
            jednorazove: {
                urnove: { bezDph: 39.99, sDph: 47.99 },
                jednohrob_nesavy: { bezDph: 49.99, sDph: 59.99 },
                jednohrob_savy: { bezDph: 54.99, sDph: 65.99 },
                dvojhrob_nesavy: { bezDph: 64.99, sDph: 77.99 },
                dvojhrob_savy: { bezDph: 69.99, sDph: 83.99 }
            },
            duo: {
                urnove: { bezDph: 74.99, sDph: 89.99 },
                jednohrob_nesavy: { bezDph: 94.99, sDph: 113.99 },
                jednohrob_savy: { bezDph: 98.99, sDph: 118.79 },
                dvojhrob_nesavy: { bezDph: 119.99, sDph: 143.99 },
                dvojhrob_savy: { bezDph: 124.99, sDph: 149.99 }
            },
            stvrtrocne: {
                urnove: { bezDph: 149.99, sDph: 179.99 },
                jednohrob_nesavy: { bezDph: 189.99, sDph: 227.99 },
                jednohrob_savy: { bezDph: 199.99, sDph: 239.99 },
                dvojhrob_nesavy: { bezDph: 239.99, sDph: 287.99 },
                dvojhrob_savy: { bezDph: 259.99, sDph: 311.99 }
            },
            stvrtrocne_akcia: {
                urnove: { bezDph: 299.98, sDph: 359.98 },
                jednohrob_nesavy: { bezDph: 379.98, sDph: 455.98 },
                jednohrob_savy: { bezDph: 399.98, sDph: 479.98 },
                dvojhrob_nesavy: { bezDph: 479.98, sDph: 575.98 },
                dvojhrob_savy: { bezDph: 519.98, sDph: 623.98 }
            },
            mesacne: {
                urnove: { bezDph: 329.99, sDph: 395.99 },
                jednohrob_nesavy: { bezDph: 429.99, sDph: 515.99 },
                jednohrob_savy: { bezDph: 459.99, sDph: 551.99 },
                dvojhrob_nesavy: { bezDph: 549.99, sDph: 659.99 },
                dvojhrob_savy: { bezDph: 589.99, sDph: 707.99 }
            },
            specialna: {
                urnove: { bezDph: 659.98, sDph: 791.98 },
                jednohrob_nesavy: { bezDph: 859.98, sDph: 1031.98 },
                jednohrob_savy: { bezDph: 919.98, sDph: 1103.98 },
                dvojhrob_nesavy: { bezDph: 1099.98, sDph: 1319.98 },
                dvojhrob_savy: { bezDph: 1179.98, sDph: 1415.98 }
            }
        };

        const GRAVE_TYPES = {
            urnove: 'Urnové miesto',
            jednohrob_nesavy: 'Jednohrob (nesavý)',
            jednohrob_savy: 'Jednohrob (savý)',
            dvojhrob_nesavy: 'Dvojhrob (nesavý)',
            dvojhrob_savy: 'Dvojhrob (savý)'
        };

        const SERVICE_TYPES = {
            jednorazove: 'Jednorazové umytie (1× ročne)',
            duo: 'DUO umytie (2× ročne)',
            stvrtrocne: 'Štvrťročná starostlivosť (4× ročne)',
            stvrtrocne_akcia: 'Štvrťročná starostlivosť 2+1 rok zadarmo (3 roky)',
            mesacne: 'Každý mesiac (12× ročne)',
            specialna: 'Špeciálna ponuka 2+1 zdarma (3 roky)'
        };

        function App() {
            const [showDph, setShowDph] = useState(true);
            const [clientData, setClientData] = useState({
                name: '',
                phone: '',
                email: '',
                address: ''
            });
            const [graves, setGraves] = useState([{
                id: 1,
                type: 'urnove',
                service: 'jednorazove',
                location: ''
            }]);
            const [additionalServices, setAdditionalServices] = useState({
                sviatocne: false,
                pisma: false,
                impregnacia: false
            });
            const [notes, setNotes] = useState('');

            const addGrave = () => {
                const newId = Math.max(...graves.map(g => g.id)) + 1;
                setGraves([...graves, {
                    id: newId,
                    type: 'urnove',
                    service: 'jednorazove',
                    location: ''
                }]);
            };

            const removeGrave = (id) => {
                if (graves.length > 1) {
                    setGraves(graves.filter(g => g.id !== id));
                }
            };

            const updateGrave = (id, field, value) => {
                setGraves(graves.map(g => 
                    g.id === id ? { ...g, [field]: value } : g
                ));
            };

            const calculateTotal = () => {
                let total = 0;
                graves.forEach(grave => {
                    const price = PRICES[grave.service][grave.type];
                    total += showDph ? price.sDph : price.bezDph;
                });
                
                // Doplnkové služby
                if (additionalServices.sviatocne) total += showDph ? 59.99 : 49.99;
                if (additionalServices.pisma) total += showDph ? 119.99 : 99.99;
                if (additionalServices.impregnacia) total += showDph ? 71.99 : 59.99;
                
                return total;
            };

            const generatePDF = () => {
                // Check if html2pdf is available
                if (typeof window.html2pdf === 'undefined') {
                    alert('Chyba: html2pdf knižnica nie je načítaná. Skúste obnoviť stránku.');
                    return;
                }

                // Validate required fields
                if (!clientData.name || !clientData.phone) {
                    alert('Prosím vyplňte meno a telefón zákazníka.');
                    return;
                }

                if (graves.length === 0) {
                    alert('Prosím pridajte aspoň jeden hrob.');
                    return;
                }

                // Create PDF content
                const pdfContent = createPDFContent({
                    clientData,
                    graves,
                    additionalServices,
                    notes,
                    showDph,
                    total: calculateTotal()
                });

                // Generate PDF
                const opt = {
                    margin: 0.5,
                    filename: `cenova-ponuka-ehroby-${clientData.name.replace(/\s+/g, '-').toLowerCase()}.pdf`,
                    image: { type: 'jpeg', quality: 0.98 },
                    html2canvas: {
                        scale: 2,
                        useCORS: true,
                        letterRendering: true,
                        allowTaint: true
                    },
                    jsPDF: {
                        unit: 'in',
                        format: 'a4',
                        orientation: 'portrait',
                        compress: true
                    }
                };

                html2pdf().set(opt).from(pdfContent).save();
            };

            const createPDFContent = (data) => {
                const today = new Date().toLocaleDateString('sk-SK');

                return `
                    <div style="font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 800px; margin: 0 auto; padding: 30px; color: #1e293b; background: white;">
                        <!-- Header with Logo -->
                        <div style="display: flex; align-items: center; margin-bottom: 40px; border-bottom: 3px solid #5e2e60; padding-bottom: 25px;">
                            <img src="https://storage.googleapis.com/espomienka/logo36.png" alt="eHroby Logo" style="width: 60px; height: 32px; margin-right: 20px;" onerror="this.style.display='none'">
                            <div style="flex: 1;">
                                <h1 style="color: #5e2e60; font-size: 32px; margin: 0; font-weight: 700; letter-spacing: -0.5px;">eHroby</h1>
                                <p style="color: #5f8132; font-size: 16px; margin: 5px 0; font-weight: 500;">vytvárame pokojné spomienky</p>
                            </div>
                            <div style="text-align: right;">
                                <h2 style="color: #327881; font-size: 24px; margin: 0; font-weight: 600;">Cenová ponuka</h2>
                                <p style="color: #6b7280; font-size: 14px; margin: 5px 0;">Dátum: ${today}</p>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div style="display: flex; justify-content: space-between; margin-bottom: 35px; font-size: 13px; color: #6b7280;">
                            <div style="background: #f8fafc; padding: 15px; border-radius: 8px; border-left: 4px solid #5e2e60;">
                                <h4 style="color: #5e2e60; margin: 0 0 8px 0; font-size: 14px;">Kontakt</h4>
                                <p style="margin: 3px 0;"><strong>Vladimír Seman</strong></p>
                                <p style="margin: 3px 0;">📞 +421 951 553 464</p>
                                <p style="margin: 3px 0;">✉️ <EMAIL></p>
                            </div>
                        </div>

                        <!-- Customer Information -->
                        <div style="margin-bottom: 35px; background: #f8fafc; padding: 20px; border-radius: 12px; border-left: 4px solid #5e2e60;">
                            <h3 style="color: #5e2e60; font-size: 18px; margin: 0 0 15px 0; font-weight: 600;">Údaje o zákazníkovi</h3>
                            <div style="font-size: 15px; line-height: 1.6;">
                                <p style="margin: 8px 0;"><strong>Meno:</strong> ${data.clientData.name}</p>
                                <p style="margin: 8px 0;"><strong>Telefón:</strong> ${data.clientData.phone}</p>
                                ${data.clientData.email ? `<p style="margin: 8px 0;"><strong>Email:</strong> ${data.clientData.email}</p>` : ''}
                                ${data.clientData.address ? `<p style="margin: 8px 0;"><strong>Adresa:</strong> ${data.clientData.address}</p>` : ''}
                            </div>
                        </div>

                        <!-- Services -->
                        <div style="margin-bottom: 35px;">
                            <h3 style="color: #5e2e60; font-size: 18px; margin: 0 0 20px 0; font-weight: 600;">Vybrané služby</h3>
                            <table style="width: 100%; border-collapse: collapse; font-size: 14px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border-radius: 8px; overflow: hidden;">
                                <thead>
                                    <tr style="background: linear-gradient(135deg, #5e2e60, #7c3aed);">
                                        <th style="padding: 15px; text-align: left; color: white; font-weight: 600; font-size: 15px;">Služba</th>
                                        <th style="padding: 15px; text-align: center; color: white; font-weight: 600; font-size: 15px;">Typ hrobu</th>
                                        <th style="padding: 15px; text-align: right; color: white; font-weight: 600; font-size: 15px;">Cena</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${data.graves.map((grave, index) => {
                                        const price = PRICES[grave.service][grave.type];
                                        const displayPrice = data.showDph ? price.sDph : price.bezDph;
                                        const backgroundColor = index % 2 === 0 ? '#ffffff' : '#f8fafc';

                                        return `
                                            <tr style="background: ${backgroundColor};">
                                                <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; font-weight: 500;">${SERVICE_TYPES[grave.service]}</td>
                                                <td style="padding: 15px; text-align: center; border-bottom: 1px solid #e5e7eb; color: #6b7280;">${GRAVE_TYPES[grave.type]}</td>
                                                <td style="padding: 15px; text-align: right; border-bottom: 1px solid #e5e7eb; color: #5f8132; font-weight: 600; font-size: 15px;">${displayPrice.toFixed(2)} €</td>
                                            </tr>
                                            ${grave.location ? `
                                                <tr style="background: ${backgroundColor};">
                                                    <td colspan="3" style="padding: 8px 15px 15px 30px; border-bottom: 1px solid #e5e7eb; color: #6b7280; font-size: 13px; font-style: italic;">
                                                        📍 Lokalita: ${grave.location}
                                                    </td>
                                                </tr>
                                            ` : ''}
                                        `;
                                    }).join('')}
                                </tbody>
                            </table>
                        </div>

                        ${(data.additionalServices.sviatocne || data.additionalServices.pisma || data.additionalServices.impregnacia) ? `
                            <!-- Additional Services -->
                            <div style="margin-bottom: 35px;">
                                <h3 style="color: #5e2e60; font-size: 18px; margin: 0 0 20px 0; font-weight: 600;">Doplnkové služby</h3>
                                <table style="width: 100%; border-collapse: collapse; font-size: 14px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border-radius: 8px; overflow: hidden;">
                                    <tbody>
                                        ${data.additionalServices.sviatocne ? `
                                            <tr style="background: #ffffff;">
                                                <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; font-weight: 500;">🎄 Sviatočné čistenie</td>
                                                <td style="padding: 15px; text-align: right; border-bottom: 1px solid #e5e7eb; color: #5f8132; font-weight: 600; font-size: 15px;">${data.showDph ? '59,99' : '49,99'} €</td>
                                            </tr>
                                        ` : ''}
                                        ${data.additionalServices.pisma ? `
                                            <tr style="background: #f8fafc;">
                                                <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; font-weight: 500;">✍️ Obnova písma</td>
                                                <td style="padding: 15px; text-align: right; border-bottom: 1px solid #e5e7eb; color: #5f8132; font-weight: 600; font-size: 15px;">${data.showDph ? '119,99' : '99,99'} €</td>
                                            </tr>
                                        ` : ''}
                                        ${data.additionalServices.impregnacia ? `
                                            <tr style="background: #ffffff;">
                                                <td style="padding: 15px; border-bottom: 1px solid #e5e7eb; font-weight: 500;">🛡️ Impregnácia kameňa</td>
                                                <td style="padding: 15px; text-align: right; border-bottom: 1px solid #e5e7eb; color: #5f8132; font-weight: 600; font-size: 15px;">${data.showDph ? '71,99' : '59,99'} €</td>
                                            </tr>
                                        ` : ''}
                                    </tbody>
                                </table>
                            </div>
                        ` : ''}

                        <!-- Price Summary -->
                        <div style="margin-bottom: 35px; background: linear-gradient(135deg, #f8fafc, #ffffff); padding: 25px; border-radius: 12px; border: 2px solid #e5e7eb;">
                            <h3 style="color: #5e2e60; font-size: 18px; margin: 0 0 20px 0; font-weight: 600;">Súhrn cien</h3>
                            <table style="width: 100%; font-size: 15px;">
                                ${data.showDph ? `
                                    <tr>
                                        <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb;">Základ dane:</td>
                                        <td style="padding: 8px 0; text-align: right; border-bottom: 1px solid #e5e7eb; font-weight: 500;">${(data.total / 1.2).toFixed(2)} €</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; border-bottom: 1px solid #e5e7eb;">DPH 20%:</td>
                                        <td style="padding: 8px 0; text-align: right; border-bottom: 1px solid #e5e7eb; font-weight: 500;">${(data.total - data.total / 1.2).toFixed(2)} €</td>
                                    </tr>
                                ` : ''}
                                <tr style="font-size: 20px; font-weight: 700; color: #5e2e60;">
                                    <td style="padding: 20px 0; border-top: 3px solid #5e2e60;">Celkom ${data.showDph ? 's DPH' : 'bez DPH'}:</td>
                                    <td style="padding: 20px 0; text-align: right; border-top: 3px solid #5e2e60;">${data.total.toFixed(2)} €</td>
                                </tr>
                            </table>
                        </div>

                        ${data.notes.trim() ? `
                            <!-- Notes -->
                            <div style="margin-bottom: 35px; background: #fffbeb; padding: 20px; border-radius: 12px; border-left: 4px solid #f59e0b;">
                                <h3 style="color: #92400e; font-size: 16px; margin: 0 0 10px 0; font-weight: 600;">📝 Poznámky</h3>
                                <p style="color: #78350f; font-size: 14px; line-height: 1.6; margin: 0;">${data.notes}</p>
                            </div>
                        ` : ''}

                        <!-- Footer -->
                        <div style="text-align: center; font-size: 12px; color: #6b7280; border-top: 2px solid #e5e7eb; padding-top: 25px; margin-top: 40px;">
                            <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 15px;">
                                <p style="margin: 5px 0; font-weight: 600; color: #5e2e60;">🙏 Ďakujeme za Váš záujem o naše služby!</p>
                                <p style="margin: 5px 0;">Pre viac informácií nás kontaktujte na <strong>+421 951 553 464</strong> alebo <strong><EMAIL></strong></p>
                            </div>
                            <p style="margin: 5px 0; font-style: italic; color: #9ca3af;">Ponuka platná 30 dní od dátumu vystavenia</p>
                            <p style="margin: 5px 0; font-weight: 500; color: #5e2e60;">eHroby - vytvárame pokojné spomienky</p>
                        </div>
                    </div>
                `;
            };

            return (
                <div className="app">
                    <header className="header">
                        <div className="container">
                            <h1><i className="fas fa-file-invoice"></i> eHroby - Generátor cenových ponúk</h1>
                            <p>Profesionálne služby starostlivosti o hrobové miesta</p>
                        </div>
                    </header>

                    <main className="main">
                        <div className="container">
                            <div className="form-section">
                                <h2><i className="fas fa-user"></i> Údaje klienta</h2>
                                <div className="form-grid">
                                    <div className="form-group">
                                        <label>Meno a priezvisko *</label>
                                        <input
                                            type="text"
                                            value={clientData.name}
                                            onChange={(e) => setClientData({...clientData, name: e.target.value})}
                                            placeholder="Zadajte meno a priezvisko"
                                        />
                                    </div>
                                    <div className="form-group">
                                        <label>Telefón *</label>
                                        <input
                                            type="tel"
                                            value={clientData.phone}
                                            onChange={(e) => setClientData({...clientData, phone: e.target.value})}
                                            placeholder="+421 xxx xxx xxx"
                                        />
                                    </div>
                                    <div className="form-group">
                                        <label>Email</label>
                                        <input
                                            type="email"
                                            value={clientData.email}
                                            onChange={(e) => setClientData({...clientData, email: e.target.value})}
                                            placeholder="<EMAIL>"
                                        />
                                    </div>
                                    <div className="form-group">
                                        <label>Adresa hrobových miest</label>
                                        <input
                                            type="text"
                                            value={clientData.address}
                                            onChange={(e) => setClientData({...clientData, address: e.target.value})}
                                            placeholder="Cintorín, mesto"
                                        />
                                    </div>
                                </div>
                            </div>

                            <div className="form-section">
                                <div className="section-header">
                                    <h2><i className="fas fa-cross"></i> Výber služieb</h2>
                                    <div className="price-toggle">
                                        <label className="toggle-switch">
                                            <input
                                                type="checkbox"
                                                checked={showDph}
                                                onChange={(e) => setShowDph(e.target.checked)}
                                            />
                                            <span className="slider"></span>
                                        </label>
                                        <span>Zobraziť ceny {showDph ? 's DPH' : 'bez DPH'}</span>
                                    </div>
                                </div>

                                {graves.map((grave, index) => (
                                    <div key={grave.id} className="grave-item">
                                        <div className="grave-header">
                                            <h3>Hrob #{index + 1}</h3>
                                            {graves.length > 1 && (
                                                <button
                                                    className="btn-remove"
                                                    onClick={() => removeGrave(grave.id)}
                                                >
                                                    <i className="fas fa-trash"></i>
                                                </button>
                                            )}
                                        </div>

                                        <div className="form-grid">
                                            <div className="form-group">
                                                <label>Typ hrobu</label>
                                                <select
                                                    value={grave.type}
                                                    onChange={(e) => updateGrave(grave.id, 'type', e.target.value)}
                                                >
                                                    {Object.entries(GRAVE_TYPES).map(([key, label]) => (
                                                        <option key={key} value={key}>{label}</option>
                                                    ))}
                                                </select>
                                            </div>

                                            <div className="form-group">
                                                <label>Frekvencia služby</label>
                                                <select
                                                    value={grave.service}
                                                    onChange={(e) => updateGrave(grave.id, 'service', e.target.value)}
                                                >
                                                    {Object.entries(SERVICE_TYPES).map(([key, label]) => (
                                                        <option key={key} value={key}>{label}</option>
                                                    ))}
                                                </select>
                                            </div>

                                            <div className="form-group">
                                                <label>Lokalita hrobu</label>
                                                <input
                                                    type="text"
                                                    value={grave.location}
                                                    onChange={(e) => updateGrave(grave.id, 'location', e.target.value)}
                                                    placeholder="Napr. sektor A, rad 5, číslo 12"
                                                />
                                            </div>

                                            <div className="price-display">
                                                <span className="price">
                                                    {showDph
                                                        ? PRICES[grave.service][grave.type].sDph.toFixed(2)
                                                        : PRICES[grave.service][grave.type].bezDph.toFixed(2)
                                                    } €
                                                </span>
                                                <small>{showDph ? 's DPH' : 'bez DPH'}</small>
                                            </div>
                                        </div>
                                    </div>
                                ))}

                                <button className="btn-add" onClick={addGrave}>
                                    <i className="fas fa-plus"></i> Pridať ďalší hrob
                                </button>
                            </div>

                            <div className="form-section">
                                <h2><i className="fas fa-plus-circle"></i> Doplnkové služby</h2>
                                <div className="additional-services">
                                    <label className="checkbox-item">
                                        <input
                                            type="checkbox"
                                            checked={additionalServices.sviatocne}
                                            onChange={(e) => setAdditionalServices({
                                                ...additionalServices,
                                                sviatocne: e.target.checked
                                            })}
                                        />
                                        <span className="checkmark"></span>
                                        <span className="service-name">Sviatočné čistenie</span>
                                        <span className="service-price">
                                            {showDph ? '59,99' : '49,99'} €
                                        </span>
                                    </label>

                                    <label className="checkbox-item">
                                        <input
                                            type="checkbox"
                                            checked={additionalServices.pisma}
                                            onChange={(e) => setAdditionalServices({
                                                ...additionalServices,
                                                pisma: e.target.checked
                                            })}
                                        />
                                        <span className="checkmark"></span>
                                        <span className="service-name">Obnova písma</span>
                                        <span className="service-price">
                                            {showDph ? '119,99' : '99,99'} €
                                        </span>
                                    </label>

                                    <label className="checkbox-item">
                                        <input
                                            type="checkbox"
                                            checked={additionalServices.impregnacia}
                                            onChange={(e) => setAdditionalServices({
                                                ...additionalServices,
                                                impregnacia: e.target.checked
                                            })}
                                        />
                                        <span className="checkmark"></span>
                                        <span className="service-name">Impregnácia kameňa</span>
                                        <span className="service-price">
                                            {showDph ? '71,99' : '59,99'} €
                                        </span>
                                    </label>
                                </div>
                            </div>

                            <div className="form-section">
                                <h2><i className="fas fa-sticky-note"></i> Poznámky</h2>
                                <textarea
                                    value={notes}
                                    onChange={(e) => setNotes(e.target.value)}
                                    placeholder="Špeciálne požiadavky alebo poznámky..."
                                    rows="4"
                                ></textarea>
                            </div>

                            <div className="summary-section">
                                <div className="total-price">
                                    <h2>Celková suma: {calculateTotal().toFixed(2)} €</h2>
                                    <p>{showDph ? 'Cena s DPH (20%)' : 'Cena bez DPH'}</p>

                                    {showDph && (
                                        <div className="price-breakdown">
                                            <div>Základ dane: {(calculateTotal() / 1.2).toFixed(2)} €</div>
                                            <div>DPH (20%): {(calculateTotal() - calculateTotal() / 1.2).toFixed(2)} €</div>
                                        </div>
                                    )}
                                </div>

                                <button
                                    className="btn-generate"
                                    onClick={() => generatePDF()}
                                    disabled={!clientData.name || !clientData.phone}
                                >
                                    <i className="fas fa-file-pdf"></i> Generovať PDF ponuku
                                </button>
                            </div>
                        </div>
                    </main>

                    <footer className="footer">
                        <div className="container">
                            <p>&copy; 2024 eHroby - Vladimír Seman | +421 951 553 464 | <EMAIL></p>
                        </div>
                    </footer>
                </div>
            );
        }

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
