<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>eHroby - <PERSON><PERSON><PERSON><PERSON> cenov<PERSON>ch pon<PERSON></title>
    <meta name="description" content="Profesionálny generátor PDF cenových ponúk pre služby starostlivosti o hrobové miesta">
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState } = React;

        // Cenové tabuľky podľa špecifikácie
        const PRICES = {
            jednorazove: {
                urnove: { bezDph: 39.99, sDph: 47.99 },
                jednohrob_nesavy: { bezDph: 49.99, sDph: 59.99 },
                jednohrob_savy: { bezDph: 54.99, sDph: 65.99 },
                dvojhrob_nesavy: { bezDph: 64.99, sDph: 77.99 },
                dvojhrob_savy: { bezDph: 69.99, sDph: 83.99 }
            },
            duo: {
                urnove: { bezDph: 74.99, sDph: 89.99 },
                jednohrob_nesavy: { bezDph: 94.99, sDph: 113.99 },
                jednohrob_savy: { bezDph: 98.99, sDph: 118.79 },
                dvojhrob_nesavy: { bezDph: 119.99, sDph: 143.99 },
                dvojhrob_savy: { bezDph: 124.99, sDph: 149.99 }
            },
            stvrtrocne: {
                urnove: { bezDph: 149.99, sDph: 179.99 },
                jednohrob_nesavy: { bezDph: 189.99, sDph: 227.99 },
                jednohrob_savy: { bezDph: 199.99, sDph: 239.99 },
                dvojhrob_nesavy: { bezDph: 239.99, sDph: 287.99 },
                dvojhrob_savy: { bezDph: 259.99, sDph: 311.99 }
            },
            stvrtrocne_akcia: {
                urnove: { bezDph: 299.98, sDph: 359.98 },
                jednohrob_nesavy: { bezDph: 379.98, sDph: 455.98 },
                jednohrob_savy: { bezDph: 399.98, sDph: 479.98 },
                dvojhrob_nesavy: { bezDph: 479.98, sDph: 575.98 },
                dvojhrob_savy: { bezDph: 519.98, sDph: 623.98 }
            },
            mesacne: {
                urnove: { bezDph: 329.99, sDph: 395.99 },
                jednohrob_nesavy: { bezDph: 429.99, sDph: 515.99 },
                jednohrob_savy: { bezDph: 459.99, sDph: 551.99 },
                dvojhrob_nesavy: { bezDph: 549.99, sDph: 659.99 },
                dvojhrob_savy: { bezDph: 589.99, sDph: 707.99 }
            },
            specialna: {
                urnove: { bezDph: 659.98, sDph: 791.98 },
                jednohrob_nesavy: { bezDph: 859.98, sDph: 1031.98 },
                jednohrob_savy: { bezDph: 919.98, sDph: 1103.98 },
                dvojhrob_nesavy: { bezDph: 1099.98, sDph: 1319.98 },
                dvojhrob_savy: { bezDph: 1179.98, sDph: 1415.98 }
            }
        };

        const GRAVE_TYPES = {
            urnove: 'Urnové miesto',
            jednohrob_nesavy: 'Jednohrob (nesavý)',
            jednohrob_savy: 'Jednohrob (savý)',
            dvojhrob_nesavy: 'Dvojhrob (nesavý)',
            dvojhrob_savy: 'Dvojhrob (savý)'
        };

        const SERVICE_TYPES = {
            jednorazove: 'Jednorazové umytie (1× ročne)',
            duo: 'DUO umytie (2× ročne)',
            stvrtrocne: 'Štvrťročná starostlivosť (4× ročne)',
            stvrtrocne_akcia: 'Štvrťročná starostlivosť 2+1 rok zadarmo (3 roky)',
            mesacne: 'Každý mesiac (12× ročne)',
            specialna: 'Špeciálna ponuka 2+1 zdarma (3 roky)'
        };

        function App() {
            const [showDph, setShowDph] = useState(true);
            const [clientData, setClientData] = useState({
                name: '',
                phone: '',
                email: '',
                address: ''
            });
            const [graves, setGraves] = useState([{
                id: 1,
                type: 'urnove',
                service: 'jednorazove',
                location: ''
            }]);
            const [additionalServices, setAdditionalServices] = useState({
                sviatocne: false,
                pisma: false,
                impregnacia: false
            });
            const [notes, setNotes] = useState('');

            const addGrave = () => {
                const newId = Math.max(...graves.map(g => g.id)) + 1;
                setGraves([...graves, {
                    id: newId,
                    type: 'urnove',
                    service: 'jednorazove',
                    location: ''
                }]);
            };

            const removeGrave = (id) => {
                if (graves.length > 1) {
                    setGraves(graves.filter(g => g.id !== id));
                }
            };

            const updateGrave = (id, field, value) => {
                setGraves(graves.map(g => 
                    g.id === id ? { ...g, [field]: value } : g
                ));
            };

            const calculateTotal = () => {
                let total = 0;
                graves.forEach(grave => {
                    const price = PRICES[grave.service][grave.type];
                    total += showDph ? price.sDph : price.bezDph;
                });
                
                // Doplnkové služby
                if (additionalServices.sviatocne) total += showDph ? 59.99 : 49.99;
                if (additionalServices.pisma) total += showDph ? 119.99 : 99.99;
                if (additionalServices.impregnacia) total += showDph ? 71.99 : 59.99;
                
                return total;
            };

            const generatePDF = async () => {
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();

                // Funkcia pre zachovanie slovenských znakov - bez konverzie
                const convertText = (text) => {
                    if (!text) return '';
                    return text.toString();
                };

                try {
                    // Pridanie lokálneho loga
                    const logoUrl = './uprava-loga.png';
                    const logoImg = new Image();

                    await new Promise((resolve, reject) => {
                        logoImg.onload = () => {
                            try {
                                // Vytvorenie canvas pre konverziu obrázka
                                const canvas = document.createElement('canvas');
                                const ctx = canvas.getContext('2d');
                                canvas.width = logoImg.width;
                                canvas.height = logoImg.height;
                                ctx.drawImage(logoImg, 0, 0);
                                const logoDataUrl = canvas.toDataURL('image/png');

                                // Pridanie loga do PDF - väčšie a lepšie umiestnené
                                doc.addImage(logoDataUrl, 'PNG', 15, 10, 40, 21);
                                resolve();
                            } catch (error) {
                                console.warn('Chyba pri pridávaní loga:', error);
                                resolve(); // Pokračuj aj bez loga
                            }
                        };
                        logoImg.onerror = () => {
                            console.warn('Nepodarilo sa načítať logo');
                            resolve(); // Pokračuj aj bez loga
                        };
                        logoImg.src = logoUrl;
                    });
                } catch (error) {
                    console.warn('Chyba pri načítavaní loga:', error);
                }

                // Hlavička s logom
                doc.setFontSize(18);
                doc.setTextColor(50, 120, 129);
                doc.text('Cenová ponuka', 65, 35);

                // Kontaktné údaje - lepšie formátovanie
                doc.setFontSize(10);
                doc.setTextColor(0, 0, 0);
                doc.text('Vladimír Seman', 15, 45);
                doc.text('Tel: +421 951 553 464', 15, 52);
                doc.text('Email: <EMAIL>', 15, 59);

                // Dátum - zarovnané vpravo
                const today = new Date().toLocaleDateString('sk-SK');
                doc.text(`Dátum: ${today}`, 150, 45);

                // Oddeľovacia čiara
                doc.setLineWidth(0.5);
                doc.setDrawColor(94, 46, 96);
                doc.line(15, 65, 195, 65);
                
                // Údaje klienta
                let yPos = 75;
                doc.setFontSize(14);
                doc.setTextColor(94, 46, 96);
                doc.text('Údaje klienta:', 20, yPos);

                yPos += 12;
                doc.setFontSize(10);
                doc.setTextColor(0, 0, 0);
                doc.text(`Meno: ${clientData.name}`, 25, yPos);
                yPos += 7;
                doc.text(`Telefón: ${clientData.phone}`, 25, yPos);
                if (clientData.email) {
                    yPos += 7;
                    doc.text(`Email: ${clientData.email}`, 25, yPos);
                }
                if (clientData.address) {
                    yPos += 7;
                    doc.text(`Adresa: ${clientData.address}`, 25, yPos);
                }
                
                // Služby
                yPos += 20;
                doc.setFontSize(14);
                doc.setTextColor(94, 46, 96);
                doc.text('Vybrané služby:', 20, yPos);

                // Tabuľka hlavička
                yPos += 15;
                doc.setFontSize(10);
                doc.setTextColor(94, 46, 96);
                doc.text('Popis služby', 25, yPos);
                doc.text('Cena', 150, yPos);
                yPos += 5;
                doc.setLineWidth(0.3);
                doc.setDrawColor(200, 200, 200);
                doc.line(20, yPos, 180, yPos);
                yPos += 8;

                graves.forEach((grave, index) => {
                    const price = PRICES[grave.service][grave.type];
                    const displayPrice = showDph ? price.sDph : price.bezDph;

                    doc.setFontSize(10);
                    doc.setTextColor(0, 0, 0);
                    doc.text(`${index + 1}. ${GRAVE_TYPES[grave.type]} - ${SERVICE_TYPES[grave.service]}`, 25, yPos);
                    doc.setTextColor(95, 129, 50);
                    doc.text(`${displayPrice.toFixed(2)} €`, 150, yPos);
                    doc.setTextColor(0, 0, 0);

                    if (grave.location) {
                        yPos += 7;
                        doc.setFontSize(9);
                        doc.setTextColor(100, 100, 100);
                        doc.text(`   Lokalita: ${grave.location}`, 25, yPos);
                        doc.setFontSize(10);
                        doc.setTextColor(0, 0, 0);
                    }
                    yPos += 10;
                });
                
                // Doplnkové služby
                if (additionalServices.sviatocne || additionalServices.pisma || additionalServices.impregnacia) {
                    yPos += 15;
                    doc.setFontSize(14);
                    doc.setTextColor(94, 46, 96);
                    doc.text('Doplnkové služby:', 20, yPos);
                    yPos += 12;

                    doc.setFontSize(10);
                    doc.setTextColor(0, 0, 0);

                    if (additionalServices.sviatocne) {
                        doc.text('Sviatočné čistenie', 25, yPos);
                        doc.setTextColor(95, 129, 50);
                        doc.text(`${showDph ? '59,99' : '49,99'} €`, 150, yPos);
                        doc.setTextColor(0, 0, 0);
                        yPos += 8;
                    }

                    if (additionalServices.pisma) {
                        doc.text('Obnova písma', 25, yPos);
                        doc.setTextColor(95, 129, 50);
                        doc.text(`${showDph ? '119,99' : '99,99'} €`, 150, yPos);
                        doc.setTextColor(0, 0, 0);
                        yPos += 8;
                    }

                    if (additionalServices.impregnacia) {
                        doc.text('Impregnácia kameňa', 25, yPos);
                        doc.setTextColor(95, 129, 50);
                        doc.text(`${showDph ? '71,99' : '59,99'} €`, 150, yPos);
                        doc.setTextColor(0, 0, 0);
                        yPos += 8;
                    }
                }

                // Oddeľovacia čiara pred sumou
                yPos += 15;
                doc.setLineWidth(0.5);
                doc.setDrawColor(94, 46, 96);
                doc.line(20, yPos, 180, yPos);
                yPos += 10;

                // Celková suma
                doc.setFontSize(16);
                doc.setTextColor(94, 46, 96);
                const total = calculateTotal();
                doc.text(`Celková suma: ${total.toFixed(2)} €`, 20, yPos);

                if (showDph) {
                    yPos += 12;
                    doc.setFontSize(10);
                    doc.setTextColor(100, 100, 100);
                    const bezDph = total / 1.2;
                    const dph = total - bezDph;
                    doc.text(`Základ dane: ${bezDph.toFixed(2)} €`, 25, yPos);
                    yPos += 7;
                    doc.text(`DPH (20%): ${dph.toFixed(2)} €`, 25, yPos);
                }

                // Poznámky
                if (notes.trim()) {
                    yPos += 20;
                    doc.setFontSize(14);
                    doc.setTextColor(94, 46, 96);
                    doc.text('Poznámky:', 20, yPos);
                    yPos += 12;
                    doc.setFontSize(10);
                    doc.setTextColor(0, 0, 0);

                    // Rozdelenie dlhých poznámok na riadky
                    const noteLines = doc.splitTextToSize(notes, 160);
                    noteLines.forEach(line => {
                        doc.text(line, 25, yPos);
                        yPos += 7;
                    });
                }

                // Footer s lepším formátovaním
                yPos += 25;
                doc.setLineWidth(0.3);
                doc.setDrawColor(200, 200, 200);
                doc.line(15, yPos, 195, yPos);
                yPos += 10;

                doc.setFontSize(9);
                doc.setTextColor(100, 100, 100);
                doc.text('eHroby - vytvárame pokojné spomienky', 20, yPos);
                doc.text('Ponuka platná 30 dní od dátumu vystavenia', 20, yPos + 7);

                const fileName = `cenova-ponuka-ehroby-${clientData.name.replace(/\s+/g, '-')}`;
                doc.save(`${fileName}.pdf`);
            };

            return (
                <div className="app">
                    <header className="header">
                        <div className="container">
                            <h1><i className="fas fa-file-invoice"></i> eHroby - Generátor cenových ponúk</h1>
                            <p>Profesionálne služby starostlivosti o hrobové miesta</p>
                        </div>
                    </header>

                    <main className="main">
                        <div className="container">
                            <div className="form-section">
                                <h2><i className="fas fa-user"></i> Údaje klienta</h2>
                                <div className="form-grid">
                                    <div className="form-group">
                                        <label>Meno a priezvisko *</label>
                                        <input
                                            type="text"
                                            value={clientData.name}
                                            onChange={(e) => setClientData({...clientData, name: e.target.value})}
                                            placeholder="Zadajte meno a priezvisko"
                                        />
                                    </div>
                                    <div className="form-group">
                                        <label>Telefón *</label>
                                        <input
                                            type="tel"
                                            value={clientData.phone}
                                            onChange={(e) => setClientData({...clientData, phone: e.target.value})}
                                            placeholder="+421 xxx xxx xxx"
                                        />
                                    </div>
                                    <div className="form-group">
                                        <label>Email</label>
                                        <input
                                            type="email"
                                            value={clientData.email}
                                            onChange={(e) => setClientData({...clientData, email: e.target.value})}
                                            placeholder="<EMAIL>"
                                        />
                                    </div>
                                    <div className="form-group">
                                        <label>Adresa hrobových miest</label>
                                        <input
                                            type="text"
                                            value={clientData.address}
                                            onChange={(e) => setClientData({...clientData, address: e.target.value})}
                                            placeholder="Cintorín, mesto"
                                        />
                                    </div>
                                </div>
                            </div>

                            <div className="form-section">
                                <div className="section-header">
                                    <h2><i className="fas fa-cross"></i> Výber služieb</h2>
                                    <div className="price-toggle">
                                        <label className="toggle-switch">
                                            <input
                                                type="checkbox"
                                                checked={showDph}
                                                onChange={(e) => setShowDph(e.target.checked)}
                                            />
                                            <span className="slider"></span>
                                        </label>
                                        <span>Zobraziť ceny {showDph ? 's DPH' : 'bez DPH'}</span>
                                    </div>
                                </div>

                                {graves.map((grave, index) => (
                                    <div key={grave.id} className="grave-item">
                                        <div className="grave-header">
                                            <h3>Hrob #{index + 1}</h3>
                                            {graves.length > 1 && (
                                                <button
                                                    className="btn-remove"
                                                    onClick={() => removeGrave(grave.id)}
                                                >
                                                    <i className="fas fa-trash"></i>
                                                </button>
                                            )}
                                        </div>

                                        <div className="form-grid">
                                            <div className="form-group">
                                                <label>Typ hrobu</label>
                                                <select
                                                    value={grave.type}
                                                    onChange={(e) => updateGrave(grave.id, 'type', e.target.value)}
                                                >
                                                    {Object.entries(GRAVE_TYPES).map(([key, label]) => (
                                                        <option key={key} value={key}>{label}</option>
                                                    ))}
                                                </select>
                                            </div>

                                            <div className="form-group">
                                                <label>Frekvencia služby</label>
                                                <select
                                                    value={grave.service}
                                                    onChange={(e) => updateGrave(grave.id, 'service', e.target.value)}
                                                >
                                                    {Object.entries(SERVICE_TYPES).map(([key, label]) => (
                                                        <option key={key} value={key}>{label}</option>
                                                    ))}
                                                </select>
                                            </div>

                                            <div className="form-group">
                                                <label>Lokalita hrobu</label>
                                                <input
                                                    type="text"
                                                    value={grave.location}
                                                    onChange={(e) => updateGrave(grave.id, 'location', e.target.value)}
                                                    placeholder="Napr. sektor A, rad 5, číslo 12"
                                                />
                                            </div>

                                            <div className="price-display">
                                                <span className="price">
                                                    {showDph
                                                        ? PRICES[grave.service][grave.type].sDph.toFixed(2)
                                                        : PRICES[grave.service][grave.type].bezDph.toFixed(2)
                                                    } €
                                                </span>
                                                <small>{showDph ? 's DPH' : 'bez DPH'}</small>
                                            </div>
                                        </div>
                                    </div>
                                ))}

                                <button className="btn-add" onClick={addGrave}>
                                    <i className="fas fa-plus"></i> Pridať ďalší hrob
                                </button>
                            </div>

                            <div className="form-section">
                                <h2><i className="fas fa-plus-circle"></i> Doplnkové služby</h2>
                                <div className="additional-services">
                                    <label className="checkbox-item">
                                        <input
                                            type="checkbox"
                                            checked={additionalServices.sviatocne}
                                            onChange={(e) => setAdditionalServices({
                                                ...additionalServices,
                                                sviatocne: e.target.checked
                                            })}
                                        />
                                        <span className="checkmark"></span>
                                        <span className="service-name">Sviatočné čistenie</span>
                                        <span className="service-price">
                                            {showDph ? '59,99' : '49,99'} €
                                        </span>
                                    </label>

                                    <label className="checkbox-item">
                                        <input
                                            type="checkbox"
                                            checked={additionalServices.pisma}
                                            onChange={(e) => setAdditionalServices({
                                                ...additionalServices,
                                                pisma: e.target.checked
                                            })}
                                        />
                                        <span className="checkmark"></span>
                                        <span className="service-name">Obnova písma</span>
                                        <span className="service-price">
                                            {showDph ? '119,99' : '99,99'} €
                                        </span>
                                    </label>

                                    <label className="checkbox-item">
                                        <input
                                            type="checkbox"
                                            checked={additionalServices.impregnacia}
                                            onChange={(e) => setAdditionalServices({
                                                ...additionalServices,
                                                impregnacia: e.target.checked
                                            })}
                                        />
                                        <span className="checkmark"></span>
                                        <span className="service-name">Impregnácia kameňa</span>
                                        <span className="service-price">
                                            {showDph ? '71,99' : '59,99'} €
                                        </span>
                                    </label>
                                </div>
                            </div>

                            <div className="form-section">
                                <h2><i className="fas fa-sticky-note"></i> Poznámky</h2>
                                <textarea
                                    value={notes}
                                    onChange={(e) => setNotes(e.target.value)}
                                    placeholder="Špeciálne požiadavky alebo poznámky..."
                                    rows="4"
                                ></textarea>
                            </div>

                            <div className="summary-section">
                                <div className="total-price">
                                    <h2>Celková suma: {calculateTotal().toFixed(2)} €</h2>
                                    <p>{showDph ? 'Cena s DPH (20%)' : 'Cena bez DPH'}</p>

                                    {showDph && (
                                        <div className="price-breakdown">
                                            <div>Základ dane: {(calculateTotal() / 1.2).toFixed(2)} €</div>
                                            <div>DPH (20%): {(calculateTotal() - calculateTotal() / 1.2).toFixed(2)} €</div>
                                        </div>
                                    )}
                                </div>

                                <button
                                    className="btn-generate"
                                    onClick={() => generatePDF()}
                                    disabled={!clientData.name || !clientData.phone}
                                >
                                    <i className="fas fa-file-pdf"></i> Generovať PDF ponuku
                                </button>
                            </div>
                        </div>
                    </main>

                    <footer className="footer">
                        <div className="container">
                            <p>&copy; 2024 eHroby - Vladimír Seman | +421 951 553 464 | <EMAIL></p>
                        </div>
                    </footer>
                </div>
            );
        }

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
